import Image from "next/image";
import Link from "next/link";

export default function Navbar() {
  return (
    <nav className="absolute top-0 left-0 right-0 z-10 flex items-center justify-between p-8 bg-gradient-to-b from-black to-transparent">
      <div className="flex items-center space-x-4">
        {/* Placeholder for icons - replace with actual SVG/components later */}
        <button className="text-white hover:text-primary-pink transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
        </button>
        <button className="text-white hover:text-primary-pink transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
        <button className="text-white hover:text-primary-pink transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        </button>
      </div>

      {/* Center: Navigation Links */}
      <div className="flex items-center mx-auto" dir="rtl">
        <ul className="flex space-x-6 text-white text-lg">
          <li>
            <Link href="#" className="hover:text-primary-pink transition-colors active-link">
              الرئيسية
            </Link>
          </li>
          <li>
            <Link href="#" className="hover:text-primary-pink transition-colors">
              سلسلات
            </Link>
          </li>
          <li>
            <Link href="#" className="hover:text-primary-pink transition-colors">
              أفلام
            </Link>
          </li>
          <li>
            <Link href="#" className="hover:text-primary-pink transition-colors">
              مسرحيات
            </Link>
          </li>
          <li>
            <Link href="#" className="hover:text-primary-pink transition-colors">
              وثائقيات
            </Link>
          </li>
          <li>
            <Link href="#" className="hover:text-primary-pink transition-colors">
              ترفيه
            </Link>
          </li>
          <li>
            <Link href="#" className="hover:text-primary-pink transition-colors">
              قائمتي
            </Link>
          </li>
        </ul>
      </div>
{/* Left side: Logo */}
      <div className="flex items-center">
        <Link href="/">
          <Image src="/logo.svg" alt="Forja Logo" width={120} height={40} priority />
        </Link>
      </div>
      {/* Right side: Icons */}
      
    </nav>
  );
}
