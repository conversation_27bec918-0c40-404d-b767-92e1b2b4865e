"use client";

import { useEffect, useState } from "react";
import Image from "next/image";

interface Series {
  id: string;
  title: string;
  description: string;
  categories: string[];
  poster_image: string;
  episodes: { id: string; title: string; video_url: string }[];
}

export default function SeriesList() {
  const [seriesData, setSeriesData] = useState<Series[]>([]);

  useEffect(() => {
    async function fetchSeries() {
      const res = await fetch("/data/series.json");
      const data: Series[] = await res.json();
      setSeriesData(data);
    }
    fetchSeries();
  }, []);

  return (
    <section className="p-8 text-white">
      <h2 className="text-3xl font-bold mb-6">أحدث السلسلات</h2>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {seriesData.map((series) => (
          <div key={series.id} className="relative group cursor-pointer">
            <Image
              src={series.poster_image}
              alt={series.title}
              width={250}
              height={350}
              className="rounded-lg shadow-lg group-hover:scale-105 transition-transform duration-300"
            />
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 rounded-b-lg">
              <h3 className="text-lg font-semibold">{series.title}</h3>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
