"use client";

import { useEffect, useRef, useState } from "react";
import { FaTimes, FaPlay, FaPause, FaVolumeUp, FaVolumeMute, FaExpand, FaCompress } from "react-icons/fa";

interface Episode {
  id: string;
  title: string;
  video_url: string;
}

interface VideoModalProps {
  episode: Episode;
  episodes: Episode[];
  seriesTitle: string;
  onClose: () => void;
}

export default function VideoModal({ episode, episodes, seriesTitle, onClose }: VideoModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [currentEpisode, setCurrentEpisode] = useState<Episode>(episode);

  // Video control states
  const [isPlaying, setIsPlaying] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);

  // Frame preview states
  const [showPreview, setShowPreview] = useState(false);
  const [previewTime, setPreviewTime] = useState(0);
  const [previewPosition, setPreviewPosition] = useState(0);
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const previewVideoRef = useRef<HTMLVideoElement>(null);

  // Video hover states
  const [showVideoInfo, setShowVideoInfo] = useState(false);
  const [mouseMoving, setMouseMoving] = useState(false);
  const mouseTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [onClose]);

  // Handle click outside modal
  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (event.target === modalRef.current) {
      onClose();
    }
  };

  // Auto-focus the modal when it opens
  useEffect(() => {
    if (modalRef.current) {
      modalRef.current.focus();
    }
  }, []);

  // Video hover and controls management
  useEffect(() => {
    const handleMouseMove = () => {
      setShowControls(true);
      setShowVideoInfo(true);
      setMouseMoving(true);

      // Clear existing timeout
      if (mouseTimeoutRef.current) {
        clearTimeout(mouseTimeoutRef.current);
      }

      // Set new timeout to hide controls after 3 seconds of inactivity
      mouseTimeoutRef.current = setTimeout(() => {
        if (isPlaying) {
          setShowControls(false);
          setShowVideoInfo(false);
        }
        setMouseMoving(false);
      }, 3000);
    };

    const handleMouseLeave = () => {
      if (mouseTimeoutRef.current) {
        clearTimeout(mouseTimeoutRef.current);
      }
      // Only hide controls when leaving the container in windowed mode
      if (!isFullscreen) {
        setShowControls(false);
        setShowVideoInfo(false);
        setMouseMoving(false);
      }
    };

    const handleMouseEnter = () => {
      setShowControls(true);
      setShowVideoInfo(true);
      setMouseMoving(true);
    };

    if (isFullscreen) {
      // In fullscreen mode, listen to document-level mouse events
      document.addEventListener('mousemove', handleMouseMove);

      // Initial state when entering fullscreen
      setShowControls(true);
      setShowVideoInfo(true);
      setMouseMoving(true);
    } else {
      // In windowed mode, listen to the video container
      const videoContainer = document.getElementById('video-container');
      if (videoContainer) {
        videoContainer.addEventListener('mousemove', handleMouseMove);
        videoContainer.addEventListener('mouseleave', handleMouseLeave);
        videoContainer.addEventListener('mouseenter', handleMouseEnter);
      }
    }

    return () => {
      if (mouseTimeoutRef.current) {
        clearTimeout(mouseTimeoutRef.current);
      }

      if (isFullscreen) {
        document.removeEventListener('mousemove', handleMouseMove);
      } else {
        const videoContainer = document.getElementById('video-container');
        if (videoContainer) {
          videoContainer.removeEventListener('mousemove', handleMouseMove);
          videoContainer.removeEventListener('mouseleave', handleMouseLeave);
          videoContainer.removeEventListener('mouseenter', handleMouseEnter);
        }
      }
    };
  }, [isPlaying, isFullscreen]);

  // Handle fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);

      // Reset controls state when transitioning
      if (isCurrentlyFullscreen) {
        // Entering fullscreen - show controls initially
        setShowControls(true);
        setShowVideoInfo(true);
      } else {
        // Exiting fullscreen - reset to default state
        setShowControls(true);
        setShowVideoInfo(false);
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // Video event handlers
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handlePlay = () => setIsPlaying(true);
  const handlePause = () => setIsPlaying(false);

  // Control functions
  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (videoRef.current) {
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const newTime = (clickX / rect.width) * duration;
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(videoRef.current.muted);
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
      setVolume(newVolume);
      setIsMuted(newVolume === 0);
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      // Request fullscreen on the video container
      const videoContainer = document.getElementById('video-container');
      if (videoContainer) {
        if (videoContainer.requestFullscreen) {
          videoContainer.requestFullscreen();
        } else if ((videoContainer as any).webkitRequestFullscreen) {
          (videoContainer as any).webkitRequestFullscreen();
        } else if ((videoContainer as any).mozRequestFullScreen) {
          (videoContainer as any).mozRequestFullScreen();
        } else if ((videoContainer as any).msRequestFullscreen) {
          (videoContainer as any).msRequestFullscreen();
        }
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        (document as any).mozCancelFullScreen();
      } else if ((document as any).msExitFullscreen) {
        (document as any).msExitFullscreen();
      }
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Initialize preview video element
  useEffect(() => {
    if (previewVideoRef.current && currentEpisode.video_url) {
      const previewVideo = previewVideoRef.current;
      previewVideo.src = currentEpisode.video_url;
      previewVideo.muted = true;
      previewVideo.preload = 'metadata';
    }
  }, [currentEpisode.video_url]);

  // Frame preview functions
  const generatePreviewFrame = (time: number) => {
    if (previewVideoRef.current && previewCanvasRef.current) {
      const previewVideo = previewVideoRef.current;
      const canvas = previewCanvasRef.current;
      const ctx = canvas.getContext('2d');

      if (ctx) {
        // Set canvas dimensions
        canvas.width = 160;
        canvas.height = 90; // 16:9 aspect ratio

        // Seek to the specific time
        previewVideo.currentTime = time;

        const drawFrame = () => {
          if (previewVideo.videoWidth && previewVideo.videoHeight) {
            // Calculate proper aspect ratio
            const aspectRatio = previewVideo.videoWidth / previewVideo.videoHeight;
            canvas.height = canvas.width / aspectRatio;

            // Draw the frame
            ctx.drawImage(previewVideo, 0, 0, canvas.width, canvas.height);

            // Add time overlay
            ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.fillRect(0, canvas.height - 20, canvas.width, 20);

            ctx.fillStyle = 'white';
            ctx.font = '11px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(formatTime(time), canvas.width / 2, canvas.height - 6);
          }

          previewVideo.removeEventListener('seeked', drawFrame);
        };

        previewVideo.addEventListener('seeked', drawFrame);
      }
    }
  };

  const handleProgressHover = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const hoverX = e.clientX - rect.left;
    const hoverTime = (hoverX / rect.width) * duration;
    const position = Math.max(10, Math.min(90, (hoverX / rect.width) * 100)); // Keep preview within bounds

    setPreviewTime(hoverTime);
    setPreviewPosition(position);
    setShowPreview(true);

    // Throttle frame generation to avoid performance issues
    setTimeout(() => generatePreviewFrame(hoverTime), 50);
  };

  const handleProgressLeave = () => {
    setShowPreview(false);
  };

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-95 backdrop-blur-sm"
      onClick={handleBackdropClick}
      tabIndex={-1}
    >
      <div className="relative w-full max-w-7xl mx-4 bg-gradient-to-br from-gray-900 via-black to-gray-900 rounded-2xl overflow-hidden shadow-2xl border border-gray-800 flex animate-in fade-in duration-300">
        {/* Main video section */}
        <div className="flex-1 relative">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-6 right-6 z-30 text-white hover:text-[var(--primary-pink)] transition-all duration-200 p-3 rounded-full bg-black bg-opacity-60 hover:bg-opacity-80 backdrop-blur-sm border border-gray-700 hover:border-[var(--primary-pink)] hover:scale-110"
            aria-label="Close modal"
          >
            <FaTimes className="w-5 h-5" />
          </button>

          {/* Video container */}
          <div
            className={`relative group ${isFullscreen ? 'fixed inset-0 z-50 bg-black' : 'aspect-video rounded-t-2xl overflow-hidden'}`}
            id="video-container"
          >
            <video
              ref={videoRef}
              src={currentEpisode.video_url}
              autoPlay
              className={`w-full h-full object-contain bg-black transition-all duration-200 ${
                showControls ? 'cursor-pointer' : 'cursor-none'
              }`}
              poster=""
              key={currentEpisode.id}
              onLoadedMetadata={handleLoadedMetadata}
              onTimeUpdate={handleTimeUpdate}
              onPlay={handlePlay}
              onPause={handlePause}
              onClick={togglePlayPause}
            >
              Your browser does not support the video tag.
            </video>

            {/* Video Info Overlay (shows on hover) */}
            {showVideoInfo && (
              <div className={`absolute z-20 animate-in fade-in duration-300 ${
                isFullscreen ? 'top-8 left-8' : 'top-6 left-6'
              }`}>
                <div className="bg-black bg-opacity-90 backdrop-blur-sm rounded-xl p-5 border border-gray-700 shadow-2xl">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-1 h-8 bg-gradient-to-b from-[var(--primary-purple)] to-[var(--primary-pink)] rounded-full"></div>
                    <div>
                      <h3 className={`text-white font-bold ${isFullscreen ? 'text-2xl' : 'text-lg'}`}>
                        {seriesTitle}
                      </h3>
                      <p className={`text-[var(--primary-pink)] font-medium ${isFullscreen ? 'text-base' : 'text-sm'}`}>
                        Episode {episodes.findIndex(ep => ep.id === currentEpisode.id) + 1}: {currentEpisode.title}
                      </p>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-2 text-gray-300 ${isFullscreen ? 'text-sm' : 'text-xs'}`}>
                    <span className="bg-[var(--primary-pink)] text-white px-3 py-1 rounded-full font-medium">HD</span>
                    <span>•</span>
                    <span>{episodes.length} Episodes</span>
                    <span>•</span>
                    <span className="text-[var(--primary-pink)]">Now Playing</span>
                    {isFullscreen && (
                      <>
                        <span>•</span>
                        <span className="text-yellow-400">Fullscreen</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Fullscreen Episodes List (only visible in fullscreen) */}
            {isFullscreen && showVideoInfo && (
              <div className="absolute top-8 right-8 z-20 animate-in fade-in duration-300">
                <div className="bg-black bg-opacity-90 backdrop-blur-sm rounded-xl border border-gray-700 shadow-2xl w-80 max-h-96 overflow-hidden">
                  <div className="p-4 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-900">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-1 h-6 bg-gradient-to-b from-[var(--primary-purple)] to-[var(--primary-pink)] rounded-full"></div>
                        <h3 className="text-white text-lg font-bold">Episodes</h3>
                        <span className="bg-[var(--primary-pink)] text-white text-xs px-2 py-1 rounded-full font-medium">{episodes.length}</span>
                      </div>
                    </div>
                  </div>
                  <div className="overflow-y-auto max-h-80 custom-scrollbar">
                    {episodes.map((ep, index) => (
                      <div
                        key={ep.id}
                        className={`p-4 border-b border-gray-800 cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-800 hover:to-gray-700 group ${
                          currentEpisode.id === ep.id
                            ? 'bg-gradient-to-r from-[var(--primary-purple)] from-10% to-gray-800 border-l-4 border-l-[var(--primary-pink)] shadow-lg'
                            : 'hover:border-l-4 hover:border-l-[var(--primary-pink)]'
                        }`}
                        onClick={() => setCurrentEpisode(ep)}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <div className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300 ${
                              currentEpisode.id === ep.id
                                ? 'bg-gradient-to-br from-[var(--primary-pink)] to-[var(--primary-purple)] shadow-lg'
                                : 'bg-gradient-to-br from-gray-700 to-gray-600 group-hover:from-gray-600 group-hover:to-gray-500'
                            }`}>
                              {currentEpisode.id === ep.id ? (
                                <FaPlay className="w-4 h-4 text-white" />
                              ) : (
                                <span className="text-white text-sm font-bold">{index + 1}</span>
                              )}
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className={`font-medium truncate transition-colors duration-300 text-sm ${
                              currentEpisode.id === ep.id ? 'text-white' : 'text-gray-200 group-hover:text-white'
                            }`}>
                              {ep.title}
                            </p>
                            <p className={`text-xs transition-colors duration-300 ${
                              currentEpisode.id === ep.id ? 'text-[var(--primary-pink)]' : 'text-gray-400 group-hover:text-gray-300'
                            }`}>
                              Episode {index + 1}
                              {currentEpisode.id === ep.id && <span className="ml-2">• Playing</span>}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Exit Fullscreen Button (only visible in fullscreen) */}
            {isFullscreen && showControls && (
              <button
                onClick={toggleFullscreen}
                className="absolute top-8 right-96 z-30 text-white hover:text-[var(--primary-pink)] transition-all duration-200 p-4 rounded-full bg-black bg-opacity-80 hover:bg-opacity-90 backdrop-blur-sm border border-gray-700 hover:border-[var(--primary-pink)] hover:scale-110 shadow-2xl"
                aria-label="Exit fullscreen"
              >
                <FaCompress className="w-6 h-6" />
              </button>
            )}

            {/* Custom Video Controls */}
            <div
              className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/80 to-transparent transition-all duration-300 ${
                isFullscreen ? 'p-6' : 'p-4'
              } ${showControls ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'}`}
            >
              {/* Progress Bar */}
              <div className="mb-4 relative">
                {/* Frame Preview */}
                {showPreview && (
                  <div
                    className="absolute bottom-full mb-3 transform -translate-x-1/2 z-20 animate-in fade-in duration-200"
                    style={{ left: `${previewPosition}%` }}
                  >
                    <div className="bg-gradient-to-br from-gray-900 to-black rounded-xl p-3 shadow-2xl border border-gray-700 backdrop-blur-sm">
                      <canvas
                        ref={previewCanvasRef}
                        className="rounded-lg border border-gray-600 shadow-lg"
                      />
                      <div className="text-center text-xs text-white font-medium mt-2 px-2 py-1 bg-black bg-opacity-60 rounded-md">
                        {formatTime(previewTime)}
                      </div>
                      {/* Arrow pointing down */}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-gray-900"></div>
                    </div>
                  </div>
                )}

                <div
                  className="w-full h-3 bg-gradient-to-r from-gray-800 to-gray-700 rounded-full cursor-pointer relative shadow-inner border border-gray-600 hover:h-4 transition-all duration-200"
                  onClick={handleProgressClick}
                  onMouseMove={handleProgressHover}
                  onMouseLeave={handleProgressLeave}
                >
                  <div
                    className="h-full bg-gradient-to-r from-[var(--primary-purple)] to-[var(--primary-pink)] rounded-full relative shadow-lg"
                    style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
                  >
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-5 h-5 bg-white rounded-full shadow-xl border-2 border-gray-300 hover:scale-125 transition-transform duration-200"></div>
                  </div>

                  {/* Hover indicator */}
                  {showPreview && (
                    <div
                      className="absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2 w-4 h-4 bg-white rounded-full shadow-xl border-3 border-[var(--primary-pink)] animate-pulse"
                      style={{ left: `${previewPosition}%` }}
                    ></div>
                  )}
                </div>
                <div className="flex justify-between text-xs text-[var(--text-dark-grey)] mt-1">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(duration)}</span>
                </div>
              </div>

              {/* Control Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Play/Pause Button */}
                  <button
                    onClick={togglePlayPause}
                    className="w-14 h-14 bg-gradient-to-br from-[var(--button-dark-bg)] to-gray-800 border-2 border-gray-600 rounded-full flex items-center justify-center text-white hover:border-[var(--primary-pink)] hover:bg-gradient-to-br hover:from-[var(--primary-pink)] hover:to-[var(--primary-purple)] transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105"
                  >
                    {isPlaying ? <FaPause className="w-6 h-6" /> : <FaPlay className="w-6 h-6 ml-1" />}
                  </button>

                  {/* Volume Controls */}
                  <div className="flex items-center space-x-3 bg-black bg-opacity-30 rounded-full px-4 py-2 backdrop-blur-sm border border-gray-700">
                    <button
                      onClick={toggleMute}
                      className="text-white hover:text-[var(--primary-pink)] transition-all duration-200 p-2 rounded-full hover:bg-white hover:bg-opacity-10"
                    >
                      {isMuted || volume === 0 ? <FaVolumeMute className="w-5 h-5" /> : <FaVolumeUp className="w-5 h-5" />}
                    </button>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={isMuted ? 0 : volume}
                      onChange={handleVolumeChange}
                      className="w-24 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer volume-slider"
                    />
                  </div>

                  {/* Time Display */}
                  <div className="text-white text-sm font-medium bg-black bg-opacity-40 px-3 py-2 rounded-lg backdrop-blur-sm border border-gray-700">
                    <span className="text-[var(--primary-pink)]">{formatTime(currentTime)}</span>
                    <span className="text-gray-400 mx-1">/</span>
                    <span className="text-gray-300">{formatTime(duration)}</span>
                  </div>
                </div>

                {/* Right side controls */}
                <div className="flex items-center space-x-4">
                  {/* Fullscreen Button */}
                  <button
                    onClick={toggleFullscreen}
                    className="text-white hover:text-[var(--primary-pink)] transition-all duration-200 p-3 rounded-full hover:bg-white hover:bg-opacity-10 border border-gray-700 hover:border-[var(--primary-pink)] hover:scale-110"
                  >
                    {isFullscreen ? <FaCompress className="w-5 h-5" /> : <FaExpand className="w-5 h-5" />}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Episode info */}
          <div className="p-8 bg-gradient-to-r from-gray-900 to-black border-t border-gray-800">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-[var(--primary-purple)] to-[var(--primary-pink)] rounded-full"></div>
              <div>
                <h2 className="text-3xl font-bold text-white mb-1">{seriesTitle}</h2>
                <h3 className="text-lg text-[var(--primary-pink)] font-medium">{currentEpisode.title}</h3>
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <span className="bg-[var(--primary-pink)] text-white px-3 py-1 rounded-full font-medium">HD</span>
              <span>•</span>
              <span>Episode {episodes.findIndex(ep => ep.id === currentEpisode.id) + 1} of {episodes.length}</span>
            </div>
          </div>
        </div>

        {/* Episodes list sidebar */}
        <div className="w-80 bg-gradient-to-b from-gray-900 via-gray-900 to-black border-l border-gray-700">
          <div className="p-6 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-900">
            <div className="flex items-center space-x-3">
              <div className="w-1 h-6 bg-gradient-to-b from-[var(--primary-purple)] to-[var(--primary-pink)] rounded-full"></div>
              <h3 className="text-white text-xl font-bold">Episodes</h3>
              <span className="bg-[var(--primary-pink)] text-white text-xs px-2 py-1 rounded-full font-medium">{episodes.length}</span>
            </div>
          </div>
          <div className="overflow-y-auto max-h-96 custom-scrollbar">
            {episodes.map((ep, index) => (
              <div
                key={ep.id}
                className={`p-5 border-b border-gray-800 cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-800 hover:to-gray-700 group ${
                  currentEpisode.id === ep.id
                    ? 'bg-gradient-to-r from-[var(--primary-purple)] from-10% to-gray-800 border-l-4 border-l-[var(--primary-pink)] shadow-lg'
                    : 'hover:border-l-4 hover:border-l-[var(--primary-pink)]'
                }`}
                onClick={() => setCurrentEpisode(ep)}
              >
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 ${
                      currentEpisode.id === ep.id
                        ? 'bg-gradient-to-br from-[var(--primary-pink)] to-[var(--primary-purple)] shadow-lg'
                        : 'bg-gradient-to-br from-gray-700 to-gray-600 group-hover:from-gray-600 group-hover:to-gray-500'
                    }`}>
                      {currentEpisode.id === ep.id ? (
                        <FaPlay className="w-5 h-5 text-white" />
                      ) : (
                        <span className="text-white text-sm font-bold">{index + 1}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className={`font-semibold truncate transition-colors duration-300 ${
                      currentEpisode.id === ep.id ? 'text-white' : 'text-gray-200 group-hover:text-white'
                    }`}>
                      {ep.title}
                    </p>
                    <p className={`text-sm transition-colors duration-300 ${
                      currentEpisode.id === ep.id ? 'text-[var(--primary-pink)]' : 'text-gray-400 group-hover:text-gray-300'
                    }`}>
                      Episode {index + 1}
                      {currentEpisode.id === ep.id && <span className="ml-2 text-xs">• Now Playing</span>}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Hidden preview video element */}
      <video
        ref={previewVideoRef}
        className="hidden"
        muted
        preload="metadata"
      />
    </div>
  );
}
