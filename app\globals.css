@import "tailwindcss";

:root {
  --background: #121212; /* Dark background from the image */
  --foreground: #FFFFFF; /* White text for general use */
  --primary-pink: #FF0031; /* Vibrant pink from the logo/buttons */
  --primary-purple: #5E1979; /* Deep purple from the logo/buttons */
  --text-dark-grey: #E0E0E0; /* Light grey for secondary text */
  --button-dark-bg: #282828; /* Dark background for buttons */
  --button-border-dark: #404040; /* Dark border for buttons */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #121212;
    --foreground: #FFFFFF;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

.btn-gradient {
  background-image: linear-gradient(to right, var(--primary-purple), var(--primary-pink));
}

.active-link {
  color: var(--primary-pink);
  position: relative;
}

.active-link::after {
  content: '';
  position: absolute;
  bottom: -4px; /* Adjust as needed for desired underline position */
  left: 0;
  width: 100%;
  height: 2px; /* Underline thickness */
  background-color: var(--primary-pink);
}

/* Custom Video Controls Styles */
.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(to right, var(--primary-purple), var(--primary-pink));
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(to right, var(--primary-purple), var(--primary-pink));
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.volume-slider::-webkit-slider-track {
  background: var(--button-dark-bg);
  height: 4px;
  border-radius: 2px;
}

.volume-slider::-moz-range-track {
  background: var(--button-dark-bg);
  height: 4px;
  border-radius: 2px;
  border: none;
}

/* Custom Scrollbar Styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--primary-purple), var(--primary-pink));
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--primary-pink), var(--primary-purple));
}

/* Animation classes */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-in {
  animation: fade-in 0.3s ease-out;
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s ease-out forwards;
}

