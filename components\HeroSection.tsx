"use client";

import Image from "next/image";
import { useEffect, useState, useRef } from "react";
import { FaVolumeMute, FaVolumeUp, FaCircle } from "react-icons/fa";
import VideoModal from "./VideoModal";

interface Series {
  id: string;
  title: string;
  description: string;
  categories: string[];
  poster_image: string;
  title_image: string;
  video_poster: string;
  episodes: { id: string; title: string; video_url: string }[];
}

export default function HeroSection() {
  const [seriesData, setSeriesData] = useState<Series[]>([]);
  const [currentSeries, setCurrentSeries] = useState<Series | null>(null);
  const [showVideo, setShowVideo] = useState(false);
  const [isMuted, setIsMuted] = useState(true); // Video starts muted
  const videoRef = useRef<HTMLVideoElement>(null);
  const [videoProgress, setVideoProgress] = useState(0); // State for video progress
  const [isModalOpen, setIsModalOpen] = useState(false); // State for modal
  const [animating, setAnimating] = useState(false); // State for animation

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const progress = (videoRef.current.currentTime / videoRef.current.duration) * 100;
      setVideoProgress(progress);
    }
  };

  const handleLoadedMetadata = () => {
    setVideoProgress(0); // Reset progress when new video loads
  };

  const handleWatchNow = () => {
    if (currentSeries && currentSeries.episodes.length > 0) {
      // Pause the trailer video when opening the modal
      if (videoRef.current) {
        videoRef.current.pause();
      }
      setIsModalOpen(true);
    }
  };

  const getNextSeries = () => {
    if (!currentSeries || seriesData.length === 0) return null;

    const currentIndex = seriesData.findIndex(series => series.id === currentSeries.id);
    const nextIndex = (currentIndex + 1) % seriesData.length; // Wrap around to first series after last
    return seriesData[nextIndex];
  };

  const handleTrailerEnd = () => {
    const nextSeries = getNextSeries();
    if (nextSeries) {
      setAnimating(true);
      setTimeout(() => {
        setCurrentSeries(nextSeries);
        setTimeout(() => setAnimating(false), 50);
      }, 300);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    // Resume the trailer if it was paused and hasn't ended
    if (videoRef.current && videoRef.current.paused && !videoRef.current.ended) {
      videoRef.current.play();
    }
  };

  useEffect(() => {
    async function fetchSeries() {
      const res = await fetch("/data/series.json");
      const data: Series[] = await res.json();
      setSeriesData(data);
      if (data.length > 0) {
        setCurrentSeries(data[0]);
      }
    }
    fetchSeries();
  }, []);

  useEffect(() => {
    setShowVideo(false); // Reset video state when currentSeries changes
    if (currentSeries && currentSeries.video_poster) {
      const timer = setTimeout(() => {
        setShowVideo(true);
      }, 5000); // 5 seconds
      return () => clearTimeout(timer); // Cleanup timer
    }
  }, [currentSeries]);

  if (!currentSeries) {
    return <div>Loading...</div>;
  }

  return (
    <section
      className="relative h-screen w-full flex items-center justify-center"
    >
      {showVideo && currentSeries.video_poster ? (
        <video
          ref={videoRef}
          src={currentSeries.video_poster}
          autoPlay
          muted={isMuted}
          className="absolute inset-0 w-full h-full object-cover"
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
          onEnded={handleTrailerEnd}
        />
      ) : (
        <Image
          src={currentSeries.poster_image}
          alt={currentSeries.title}
          fill
          className={`absolute inset-0 w-full h-full object-cover ${
            animating ? 'opacity-0 -translate-x-full' : 'animate-slide-in-left'
          }`}
          priority
        />
      )}
      <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-black opacity-80 "></div>
      <div className="absolute right-0 w-[80vh] flex flex-col items-end justify-center text-white p-8 text-right">
        <Image
          src={currentSeries.title_image}
          alt={currentSeries.title}
          width={400}
          height={100}
          className="mb-4"
          priority
        />
        <div className="flex justify-end space-x-2 mb-4">
          {currentSeries.categories.map((category, index) => (
            <span key={index} className="text-lg text-primary-pink">
              {category}
              {index < currentSeries.categories.length - 1 && " • "}
            </span>
          ))}
        </div>
        <p className="text-lg mb-8 leading-relaxed">{currentSeries.description}</p>
        <div className="flex justify-end space-x-4">
          <button className="bg-[var(--button-dark-bg)] border-[var(--button-border-dark)] border text-white px-8 py-3 rounded-full text-lg font-semibold flex items-center space-x-2 hover:opacity-90 transition-opacity">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>مزيدا من المعلومات</span>
          </button>
          <button
            className="btn-gradient text-white px-8 py-3 rounded-full text-lg font-semibold flex items-center space-x-2 transition-opacity hover:opacity-90"
            onClick={handleWatchNow}
          >
            <svg className="h-6 w-6" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 5V19L19 12L8 5Z" />
            </svg>
            <span>شاهد الآن</span>
          </button>
        </div>
      </div>
      {/* Combined mute button and horizontal dots */}
      <div className="absolute left-0 w-[80vh] flex flex-col items-start gap-4 justify-center text-white p-8 ">
        {showVideo && currentSeries.video_poster && (
          <button
            className="text-white text-2xl  rounded-full p-2 cursor-pointer hover:bg-opacity-75 transition-opacity"
            onClick={() => {
              if (videoRef.current) {
                videoRef.current.muted = !videoRef.current.muted;
                setIsMuted(videoRef.current.muted);
              }
            }}
          >
            {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}
          </button>
        )}
        <div className="flex space-x-2 justify-center items-center " dir="rtl"> {/* Horizontal dots */}
          {seriesData.map((series, index) => (
            currentSeries.id === series.id ? (
              <span
                key={index}
                className="cursor-pointer w-2 h-8 rounded-full bg-red-500"
              ></span>
            ) : (
              <FaCircle
                key={index}
                className="cursor-pointer text-white opacity-50 text-xs"
                onClick={() => {
                  setAnimating(true);
                  setTimeout(() => {
                    setCurrentSeries(series);
                    setTimeout(() => setAnimating(false), 50);
                  }, 300);
                }}
              />
            )
          ))}
        </div>
      </div>
      {showVideo && currentSeries.video_poster && (
        <div className="absolute bottom-0 left-0 right-0 h-1 z-30">
          <div
            className="h-full btn-gradient"
            style={{ width: `${videoProgress}%` }}
          ></div>
        </div>
      )}

      {/* Video Modal */}
      {isModalOpen && currentSeries && currentSeries.episodes.length > 0 && (
        <VideoModal
          episode={currentSeries.episodes[0]}
          episodes={currentSeries.episodes}
          seriesTitle={currentSeries.title}
          onClose={handleModalClose}
        />
      )}
    </section>
  );
}
